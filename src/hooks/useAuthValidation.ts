import { useCallback } from 'react';
import { useAuth } from '@/provider/AuthProvider';
import { ensureValidToken, getAuthDebugInfo } from '@/api/apiConfig';

/**
 * Hook for validating authentication state and ensuring valid tokens
 */
export const useAuthValidation = () => {
    const { userJwtToken, handleAuthFailure, refreshAuthToken } = useAuth();

    /**
     * Ensures we have a valid authentication token before proceeding
     * @returns Promise<boolean> - true if valid token is available, false otherwise
     */
    const ensureAuthenticated = useCallback(async (): Promise<boolean> => {
        try {
            // First check if we have a token in state
            if (!userJwtToken) {
                console.log('No token in auth state');
                return false;
            }

            // Validate and refresh token if needed
            const hasValidToken = await ensureValidToken();
            
            if (!hasValidToken) {
                console.log('Token validation failed, triggering auth failure');
                await handleAuthFailure?.();
                return false;
            }

            return true;
        } catch (error) {
            console.error('Error ensuring authentication:', error);
            await handleAuthFailure?.();
            return false;
        }
    }, [userJwtToken, handleAuthFailure]);

    /**
     * Manually refresh the authentication token
     * @returns Promise<boolean> - true if refresh was successful, false otherwise
     */
    const manualRefresh = useCallback(async (): Promise<boolean> => {
        try {
            return await refreshAuthToken?.() || false;
        } catch (error) {
            console.error('Error during manual token refresh:', error);
            return false;
        }
    }, [refreshAuthToken]);

    /**
     * Get detailed authentication debug information
     * @returns Promise<object|null> - debug info object or null if error
     */
    const getDebugInfo = useCallback(async () => {
        return await getAuthDebugInfo();
    }, []);

    /**
     * Check if user is currently authenticated (has token in state)
     * @returns boolean - true if user has token in state
     */
    const isAuthenticated = useCallback((): boolean => {
        return !!userJwtToken;
    }, [userJwtToken]);

    return {
        ensureAuthenticated,
        manualRefresh,
        getDebugInfo,
        isAuthenticated,
        userJwtToken
    };
};
