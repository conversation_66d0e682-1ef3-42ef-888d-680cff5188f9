import React, { useState } from 'react';
import { <PERSON><PERSON>, Text, YStack, XStack, ScrollView } from 'tamagui';
import { useAuthValidation } from '@/hooks/useAuthValidation';
import { AuthDebugger } from '@/utils/authDebug';

/**
 * Debug panel component for testing authentication functionality
 * This component should only be used during development
 */
export const AuthDebugPanel: React.FC = () => {
    const [debugOutput, setDebugOutput] = useState<string>('');
    const [isLoading, setIsLoading] = useState(false);
    
    const { ensureAuthenticated, manualRefresh, getDebugInfo, isAuthenticated } = useAuthValidation();

    const addToOutput = (message: string) => {
        const timestamp = new Date().toLocaleTimeString();
        setDebugOutput(prev => `[${timestamp}] ${message}\n${prev}`);
    };

    const runTest = async (testName: string, testFn: () => Promise<any>) => {
        setIsLoading(true);
        addToOutput(`Starting test: ${testName}`);
        
        try {
            const result = await testFn();
            addToOutput(`✅ ${testName} completed: ${JSON.stringify(result)}`);
        } catch (error) {
            addToOutput(`❌ ${testName} failed: ${error}`);
        } finally {
            setIsLoading(false);
        }
    };

    const clearOutput = () => {
        setDebugOutput('');
    };

    return (
        <YStack padding="$4" gap="$3" backgroundColor="$background" borderRadius="$4" margin="$4">
            <Text fontSize="$6" fontWeight="bold" color="$color">
                🔧 Auth Debug Panel
            </Text>
            
            <Text fontSize="$3" color="$gray10">
                Current Auth Status: {isAuthenticated() ? '✅ Authenticated' : '❌ Not Authenticated'}
            </Text>

            <XStack gap="$2" flexWrap="wrap">
                <Button
                    size="$3"
                    onPress={() => runTest('Full Auth Status', AuthDebugger.getFullAuthStatus)}
                    disabled={isLoading}
                >
                    Check Status
                </Button>
                
                <Button
                    size="$3"
                    onPress={() => runTest('Token Validation', AuthDebugger.testTokenValidation)}
                    disabled={isLoading}
                >
                    Test Validation
                </Button>
                
                <Button
                    size="$3"
                    onPress={() => runTest('Ensure Authenticated', ensureAuthenticated)}
                    disabled={isLoading}
                >
                    Ensure Auth
                </Button>
                
                <Button
                    size="$3"
                    onPress={() => runTest('Manual Refresh', manualRefresh)}
                    disabled={isLoading}
                >
                    Manual Refresh
                </Button>
            </XStack>

            <XStack gap="$2" flexWrap="wrap">
                <Button
                    size="$3"
                    onPress={() => runTest('Get Debug Info', getDebugInfo)}
                    disabled={isLoading}
                    theme="blue"
                >
                    Debug Info
                </Button>
                
                <Button
                    size="$3"
                    onPress={() => runTest('Simulate Expiration', AuthDebugger.simulateTokenExpiration)}
                    disabled={isLoading}
                    theme="orange"
                >
                    Simulate Expiry
                </Button>
                
                <Button
                    size="$3"
                    onPress={() => runTest('Clear Auth Data', AuthDebugger.clearAllAuthData)}
                    disabled={isLoading}
                    theme="red"
                >
                    Clear All
                </Button>
                
                <Button
                    size="$3"
                    onPress={clearOutput}
                    disabled={isLoading}
                    theme="gray"
                >
                    Clear Output
                </Button>
            </XStack>

            <YStack gap="$2">
                <Text fontSize="$4" fontWeight="bold">Debug Output:</Text>
                <ScrollView
                    maxHeight={300}
                    backgroundColor="$gray2"
                    padding="$3"
                    borderRadius="$3"
                    borderWidth={1}
                    borderColor="$gray6"
                >
                    <Text fontSize="$2" fontFamily="$mono" color="$gray12">
                        {debugOutput || 'No output yet. Click a button to run tests.'}
                    </Text>
                </ScrollView>
            </YStack>

            <Text fontSize="$2" color="$gray10" textAlign="center">
                ⚠️ This panel is for development debugging only
            </Text>
        </YStack>
    );
};
