import axios, { AxiosError } from 'axios';
import Constants from 'expo-constants';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';

// Constants for storage keys
const USER_JWT_TOKEN = "user-token";
const REFRESH_TOKEN = "refresh-token";

let baseURL = 'https://api.wullup.com/api';
let isRefreshing = false;
let failedQueue: any[] = [];

// Callback for authentication failures - will be set by AuthProvider
let authFailureCallback: (() => Promise<void>) | null = null;
let tokenUpdateCallback: ((token: string) => void) | null = null;

const getDevURL = () => {
    const debuggerHost = Constants.manifest2?.extra?.expoGo?.debuggerHost;
    if (debuggerHost) {
        const host = debuggerHost.split(':').shift();
        return `http://${host}:8001`;
    }
};


console.log("NODE_ENV", process.env.NODE_ENV);
console.log(getDevURL())

if (process.env.NODE_ENV === "development") {
    baseURL = getDevURL() + '/api';
}

export const api = axios.create({
    baseURL,
    headers: {
        'Content-Type': 'application/json',
    },
});

// Request interceptor to ensure Authorization header is set
api.interceptors.request.use(
    async (config) => {
        // If no Authorization header is set, try to get token from storage
        if (!config.headers.Authorization) {
            const token = await AsyncStorage.getItem(USER_JWT_TOKEN);
            if (token) {
                config.headers.Authorization = `Bearer ${token}`;
            }
        }
        return config;
    },
    (error) => Promise.reject(error)
);

const processQueue = (error: any, token: string | null = null) => {
    failedQueue.forEach(prom => {
        if (error) {
            prom.reject(error);
        } else {
            prom.resolve(token);
        }
    });

    failedQueue = [];
};

export const clearAuthTokens = async () => {
    try {
        await Promise.all([
            AsyncStorage.removeItem(USER_JWT_TOKEN),
            SecureStore.deleteItemAsync(REFRESH_TOKEN)
        ]);
        api.defaults.headers.common['Authorization'] = '';
    } catch (error) {
        console.error('Error clearing auth tokens:', error);
    }
};

const refreshToken = async () => {
    try {
        const refreshTokenValue = await SecureStore.getItemAsync(REFRESH_TOKEN);
        if (!refreshTokenValue) {
            throw new Error('No refresh token found');
        }

        console.log('Attempting to refresh token...');

        // Use the refreshAuthToken function from authAPI to avoid code duplication
        // We need to import it dynamically to avoid circular dependency
        const { refreshAuthToken } = await import('./authAPI');
        const response = await refreshAuthToken(refreshTokenValue);

        const { accessToken, newRefreshToken } = response;

        if (!accessToken || !newRefreshToken) {
            throw new Error('Invalid refresh response: missing tokens');
        }

        // Store new tokens using the centralized function
        await storeAuthTokens(accessToken, newRefreshToken);

        console.log('Token refresh successful');
        return accessToken;
    } catch (error: any) {
        console.error('Token refresh failed:', error.response?.data || error.message);

        // If refresh fails, clear tokens
        await clearAuthTokens();

        throw error;
    }
};

// Helper function to validate JWT token format
export const isValidJWTFormat = (token: string): boolean => {
    if (!token || typeof token !== 'string') return false;
    const parts = token.split('.');
    return parts.length === 3;
};

// Helper function to check if JWT token is expired
export const isTokenExpired = (token: string): boolean => {
    try {
        if (!isValidJWTFormat(token)) {
            console.error('Invalid JWT format');
            return true;
        }

        const payload = JSON.parse(atob(token.split('.')[1]));
        const currentTime = Date.now() / 1000;

        // Check if token has expiration claim
        if (!payload.exp) {
            console.error('Token missing expiration claim');
            return true;
        }

        const isExpired = payload.exp < currentTime;
        if (isExpired) {
            console.log('Token expired at:', new Date(payload.exp * 1000));
        }

        return isExpired;
    } catch (error) {
        console.error('Error parsing token:', error);
        return true; // Treat invalid tokens as expired
    }
};

// Helper function to validate and refresh token if needed
export const validateAndRefreshToken = async (): Promise<string | null> => {
    try {
        const storedToken = await AsyncStorage.getItem(USER_JWT_TOKEN);
        if (!storedToken) {
            console.log('No stored token found');
            return null;
        }

        console.log('Validating stored token...');

        // First check if token format is valid
        if (!isValidJWTFormat(storedToken)) {
            console.log('Invalid token format, clearing tokens');
            await clearAuthTokens();
            return null;
        }

        // Check if token is expired
        if (isTokenExpired(storedToken)) {
            console.log('Token expired, attempting refresh...');
            const newToken = await refreshToken();
            console.log('Token refreshed successfully');
            return newToken;
        }

        // Token is still valid
        console.log('Token is still valid');
        api.defaults.headers.common['Authorization'] = `Bearer ${storedToken}`;
        return storedToken;
    } catch (error) {
        console.error('Error validating token:', error);
        await clearAuthTokens();
        return null;
    }
};

// Helper function to get current auth status for debugging
export const getAuthDebugInfo = async () => {
    try {
        const [accessToken, refreshTokenValue] = await Promise.all([
            AsyncStorage.getItem(USER_JWT_TOKEN),
            SecureStore.getItemAsync(REFRESH_TOKEN)
        ]);

        return {
            hasAccessToken: !!accessToken,
            hasRefreshToken: !!refreshTokenValue,
            accessTokenExpired: accessToken ? isTokenExpired(accessToken) : null,
            authHeaderSet: !!api.defaults.headers.common['Authorization']
        };
    } catch (error) {
        console.error('Error getting auth debug info:', error);
        return null;
    }
};

// Response interceptor
api.interceptors.response.use(
    (response) => response,
    async (error: AxiosError) => {
        const originalRequest: any = error.config;

        // If error is not 401 or request has already been retried, reject
        if (!error.response || error.response.status !== 401 || originalRequest._retry) {
            return Promise.reject(error);
        }

        originalRequest._retry = true;

        // If token refresh is already in progress, queue this request
        if (isRefreshing) {
            return new Promise((resolve, reject) => {
                failedQueue.push({ resolve, reject });
            })
                .then((token) => {
                    originalRequest.headers['Authorization'] = `Bearer ${token}`;
                    return api(originalRequest);
                })
                .catch((err) => Promise.reject(err));
        }

        isRefreshing = true;

        try {
            const newToken = await refreshToken();
            processQueue(null, newToken);
            originalRequest.headers['Authorization'] = `Bearer ${newToken}`;
            return api(originalRequest);
        } catch (refreshError) {
            processQueue(refreshError, null);
            return Promise.reject(refreshError);
        } finally {
            isRefreshing = false;
        }
    }
);

// Helper functions for auth operations
export const storeAuthTokens = async (accessToken: string, refreshToken: string) => {
    try {
        await Promise.all([
            AsyncStorage.setItem(USER_JWT_TOKEN, accessToken),
            SecureStore.setItemAsync(REFRESH_TOKEN, refreshToken)
        ]);
        api.defaults.headers.common['Authorization'] = `Bearer ${accessToken}`;
    } catch (error) {
        console.error('Error storing auth tokens:', error);
        throw error;
    }
};